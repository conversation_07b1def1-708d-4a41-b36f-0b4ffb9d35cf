<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>hookup4u</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		
			
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com**********ar7ubsfi3stm9a</string>
				<string>fb000000000000</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FacebookAppID</key>
	<string>000000000000</string>
	<key>FacebookDisplayName</key>
	<string>Hookup4u</string>
	<key>LSApplicationCategoryType</key>
	<string>The iOS Simulator deployment target &apos;IPHONEOS_DEPLOYMENT_TARGET&apos; is set to 7.0, but the range of supported deployment target versions is 8.0 to 13.2.99.</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>You should never see this, please contact us</string>
	<key>NSCalendarsUsageDescription</key>
	<string>You should never see this, please contact us</string>
	<key>NSCameraUsageDescription</key>
	<string>Needs camera access to capture and set user image.</string>
	<key>NSContactsUsageDescription</key>
	<string>You should never see this, please contact us</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>You need to provide location access to search nearby users.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>You need to provide location access to search nearby users.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>You need to provide location access to search nearby users.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Needs mic permission to record user's voice.</string>
	<key>NSMotionUsageDescription</key>
	<string>You should never see this, please contact us</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Needs photos access to capture and set user image.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>You should never see this, please contact us</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>GADApplicationIdentifier</key>
			<string>ca-app-pub-0000000000000000~0000000000</string>
			
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>es</string>
	</array>
		
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
</dict>
</plist>
