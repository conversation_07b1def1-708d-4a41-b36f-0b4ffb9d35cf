{"Welcome_to_hookup4u_Please_follow_these_House_Rules": "Welcome to hookup4u.\nPlease follow these House Rules.", "By tapping 'Log in', you agree with our \n Terms.Learn how we process your data in \n our Privacy Policy and Cookies Policy.": "By tapping 'Log in', you agree with our \n Terms.Learn how we process your data in \n our Privacy Policy and Cookies Policy.", "LOG IN WITH FACEBOOK": "LOG IN WITH FACEBOOK", "LOG IN WITH PHONE NUMBER": "LOG IN WITH PHONE NUMBER", "Trouble logging in?": "Trouble logging in?", "Privacy Policy": "Privacy Policy", "Terms & Conditions": "Terms & Conditions", "English": "English", "Spanish": "Spanish", "Exit": "Exit", "Do you want to exit the app?": "Do you want to exit the app?", "No": "No", "Yes": "Yes", "Verify Your Number": "Verify Your Number", "Please enter Your mobile Number to\n receive a verification code. Message and data\n rates may apply": "Please enter Your mobile Number to\n receive a verification code. Message and data\n rates may apply", "Enter your number": "Enter your number", "CONTINUE": "CONTINUE", "Phone Number\nChanged\nSuccessfully": "Phone Number\nChanged\nSuccessfully", "Verified\n Successfully": "Verified\n Successfully", "OTP\nSent": "OTP\nSent", "Enter the code sent to ": "Enter the code sent to ", "Didn't receive the code? ": "Didn't receive the code? ", " RESEND": " RESEND", "VERIFY": "VERIFY", "Welcome to hookup4u.\nPlease follow these House Rules.": "Welcome to hookup4u.\nPlease follow these House Rules.", "Be yourself.": "Be yourself.", "Make sure your photos, age, and bio are true to who you are.": "Make sure your photos, age, and bio are true to who you are.", "Play it cool.": "Play it cool.", "Respect other and treat them as you would like to be treated": "Respect other and treat them as you would like to be treated", "Stay safe.": "Stay safe.", "Don't be too quick to give out personal information.": "Don't be too quick to give out personal information.", "Be proactive.": "Be proactive.", "Always report bad behavior.": "Always report bad behavior.", "GOT IT": "GOT IT", "My first\nname is": "My first\nname is", "Enter your first name": "Enter your first name", "This is how it will appear in App.": "This is how it will appear in App.", "My\nbirthday is": "My\nbirthday is", " Your age will be public": " Your age will be public", "I am a": "I am a", "MAN": "MAN", "WOMAN": "WOMAN", "OTHER": "OTHER", "Show my gender on my profile": "Show my gender on my profile", "Please select one": "Please select one", "My sexual\norientation is": "My sexual\norientation is", "select upto 3": "select upto 3", "Show my orientation on my profile": "Show my orientation on my profile", "Show me": "Show me", "MEN": "MEN", "WOMEN": "WOMEN", "EVERYONE": "EVERYONE", "My\nuniversity is": "My\nuniversity is", "Enter your university name": "Enter your university name", "Use current location": "Use current location", "Unable to load...": "Unable to load...", "Fetching..": "Fetching..", "Enter your city name": "Enter your city name", "Confirmation": "Confirmation", "You have successfully subscribed to our": "You have successfully subscribed to our  {} plan", "Ok": "Ok", "Select\nyour city": "Select\nyour city", "Continue": "Continue", "Select a location !": "Select a location !", "Report User": "Report User", "Is this person bothering you? Tell us what they did.": "Is this person bothering you? Tell us what they did.", "Inappropriate Photos": "Inappropriate Photos", "Feels Like Spam": "Feels Like Spam", "User is underage": "User is underage", "Other": "Other", "Additional Info(optional)": "Additional Info(optional)", "Reported": "Reported", "Notifications": "Notifications", "No Notification": "No Notification", "${user.editInfo['job_title']}${user.editInfo['company'] != null ? ' at ${user.editInfo['company']}' : ''}": "${user.editInfo['job_title']}${user.editInfo['company'] != null ? ' at ${user.editInfo['company']}' : ''}", "Living in ": "Living in {}", "${user.editInfo['DistanceVisible'] != null ? user.editInfo['DistanceVisible'] ? 'Less than ${user.distanceBW} KM away' : 'Distance not visible' : 'Less than ${user.distanceBW} KM away'}": "${user.editInfo['DistanceVisible'] != null ? user.editInfo['DistanceVisible'] ? 'Less than ${user.distanceBW} KM away' : 'Distance not visible' : 'Less than ${user.distanceBW} KM away'}", "REPORT": "REPORT {}", "There's no one new around you.": "There's no one new around you.", "NOPE": "NOPE", "LIKE": "LIKE", "It's a match\n With ": "It's a match\n With {}", "you have already used the maximum number of free available swipes for 24 hrs.": "you have already used the maximum number of free available swipes for 24 hrs.", "For swipe more users just subscribe our premium plans.": "For swipe more users just subscribe our premium plans.", "sorry, you can't access the application!": "sorry, you can't access the application!", "you're blocked by the admin and your profile will also not appear for other users.": "you're blocked by the admin and your profile will also not appear for other users.", "Skip..": "Skip..", "Enable location": "Enable location", "\nYou'll need to provide a \nlocation\nin order to search users around you.": "\nYou'll need to provide a \nlocation\nin order to search users around you.", "ALLOW LOCATION": "ALLOW LOCATION", "You'r in": "You'r in", "Phone number settings": "Phone number settings", "Phone Number": "Phone Number", "Verify Phone number": "Verify Phone number", "Verified phone number": "Verified phone number", "Update my phone number": "Update my phone number", "Settings": "Settings", "Account settings": "Account settings", "Verify Now": "Verify Now", "Verify a phone number to secure your account": "Verifique un número de teléfono para proteger su cuenta", "Discovery settings": "Discovery settings", "Current location : ": "Current location : ", "Change location": "Change location", "Change your location to see members in other city": "Change your location to see members in other city", "Man": "Man", "Woman": "Woman", "Everyone": "Everyone", "Maximum distance": "Maximum distance", "Age range": "Age range", "App settings": "App settings", "Push notifications": "Push notifications", "Change Language": "Change Language", "Invite your friends": "Invite your friends", "Look what I made!": "Look what I made!", "Logout": "Logout", "Do you want to logout your account?": "Do you want to logout your account?", "Delete Account": "Delete Account", "Do you want to delete your account?": "Do you want to delete your account?", "New address:": "New address:", "Confirm": "Confirm", "location\nchanged": "location\nchanged", "Enable to load": "Enable to load", "Add media": "Add media", "Edit Info": "Edit Info", "Check Payment Details": "Check Payment Details", "Subscribe Plan": "Subscribe Plan", "Update profile picture": "Update profile picture", "Add pictures": "Add pictures", "Select source": "Select source", " Camera": " Camera", " Gallery": " Gallery", "Can't uplaod more than 9 pictures": "Can't uplaod more than 9 pictures", "Edit Profile": "Edit Profile", "About ": "About {}", "About you": "About you", "Job title": "Job title", "Add job title": "Add job title", "Company": "Company", "Add company": "Add company", "University": "University", "Add university": "Add university", "Living in": "Living in", "Add city": "Add city", "I am": "I am", "Control your profile": "Control your profile", "Don't Show My Age": "Don't Show My Age", "Make My Distance Visible": "Make My Distance Visible", "Subscription details": "Subscription details", "Payment Summary:": "Payment Summary:", "Plan": "Plan", "Details": "Details", "Subscribed on": "Subscribed on", "Status": "Status", "Active": "Active", "Cancelled": "Cancelled", "Back": "Back", "New Matches": "New Matches", "No match found": "No match found", "Messages": "Messages", "Recent messages": "Recent messages", "Report": "Report", "Do you want to ${isBlocked ? 'Unblock' : 'Block'} ${widget.second.name}?": "Do you want to ${isBlocked ? 'Unblock' : 'Block'} ${widget.second.name}?", "Do you want to ": "Do you want to {} {}?", "You can't unblock": "You can't unblock", "Sorry You can't send message!": "Sorry You can't send message!", "Send a message...": "Send a message...", "Blocked !": "Blocked !", "Connecting...": "Connecting...", "Call Ended...": "Call Ended...", "Incoming Video Call": "Incoming Video Call", "Incoming Audio Call": "Incoming Audio Call", "is Not-answering": "{} is Not-answering", "is Busy": "{} is Busy", "Calling to": "Calling to {}...", "END": "END", "NEW": "NEW", "Photo": "Photo", "you are matched with": "you are matched with {} ", "Get matches faster": "Get matches faster", "Boost your profile once a month": "Boost your profile once a month", "more likes": "more likes", "Get free rewindes": "Get free rewindes", "Increase your chances": "Increase your chances", "Get unlimited free likes": "Get unlimited free likes", "Swipe around the world": "Swipe around the world", "Passport to anywhere with hookup4u": "Passport to anywhere with hookup4u", "highly secured": "highly secured", "Failed": "Failed", "Oops !! something went wrong. Try Again": "Oops !! something went wrong. Try Again", "Retry": "Retry", "Get our premium plans": "Get our premium plans", "Unlimited swipe.": "Unlimited swipe.", "Search users around": "Search users around {} kms.", "No active product found!!": "No active product found!!", "You must choose a subscription to continue.": "You must choose a subscription to continue.", "RESTORE PURCHASE": "RESTORE PURCHASE", "No purchase found": "No purchase found", "Past Purchases": "Past Purchases", "Straight": "Straight", "Gay": "<PERSON>", "Asexual": "Asexual", "Lesbian": "Lesbian", "Bisexual": "Bisexual", "Demisexual": "Demisexual", "Do you want to change the language to English?": "Do you want to change the language to English?", "Do you want to change the language to Spanish?": "Do you want to change the language to Spanish?", "is calling you...": "is calling you..."}