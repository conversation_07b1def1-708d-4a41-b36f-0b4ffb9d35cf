# This is a generated file; do not edit or check into version control.
agora_rtc_engine=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/agora_rtc_engine-1.0.5/
apple_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/apple_sign_in-0.1.0/
cloud_firestore=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/cloud_firestore-0.13.4+2/
cloud_firestore_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/cloud_firestore_web-0.1.1+1/
firebase_admob=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_admob-0.9.3+4/
firebase_auth=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.15.5+2/
firebase_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth_web-0.1.2/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.4+2/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core_web-0.1.1+2/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_messaging-6.0.13/
firebase_storage=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_storage-3.1.3/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_plugin_android_lifecycle-1.0.6/
flutter_ringtone_player=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_ringtone_player-2.0.0/
flutter_webview_plugin=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_webview_plugin-0.3.11/
geocoder=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/geocoder-0.2.1/
image_cropper=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/image_cropper-1.2.1/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/image_picker-0.6.3+4/
in_app_purchase=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/in_app_purchase-0.3.4/
location=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/location-3.1.0/
location_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/location_web-1.0.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/path_provider-1.6.5/
path_provider_macos=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/path_provider_macos-0.0.4/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/permission_handler-3.3.0/
share=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/share-0.6.3+6/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/sqflite-1.3.0/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher-5.5.0/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_linux-0.0.1+1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_macos-0.0.1+7/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_web-0.1.2/
